const { body, param, query, validationResult } = require('express-validator');
const ApiError = require('../utils/ApiError');

// Helper function to handle validation results
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => error.msg);
        throw new ApiError(400, errorMessages.join(', '));
    }
    next();
};

// ================================
// ASSIGNMENT SUBMISSION VALIDATION
// ================================
const validateSubmitAssignment = [
    param('assignmentId')
        .isMongoId()
        .withMessage('Invalid assignment ID'),

    body('comments')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Comments must not exceed 500 characters')
        .trim(),

    // File validation will be handled by multer middleware
    handleValidationErrors
];

// ================================
// FEEDBACK VALIDATION
// ================================
const validateFeedback = [
    body('trainerId')
        .isMongoId()
        .withMessage('Invalid trainer ID'),

    body('batchId')
        .isMongoId()
        .withMessage('Invalid batch ID'),

    body('rating')
        .isInt({ min: 1, max: 5 })
        .withMessage('Rating must be between 1 and 5'),

    body('comments')
        .optional()
        .isLength({ min: 10, max: 1000 })
        .withMessage('Comments must be between 10 and 1000 characters')
        .trim(),

    body('suggestions')
        .optional()
        .isLength({ max: 500 })
        .withMessage('Suggestions must not exceed 500 characters')
        .trim(),

    body('anonymous')
        .optional()
        .isBoolean()
        .withMessage('Anonymous must be a boolean value'),

    handleValidationErrors
];

// ================================
// CERTIFICATE REQUEST VALIDATION
// ================================
const validateCertificateRequest = [
    body('batchId')
        .isMongoId()
        .withMessage('Invalid batch ID'),

    body('certificateType')
        .optional()
        .isIn(['completion', 'participation', 'achievement'])
        .withMessage('Invalid certificate type. Must be completion, participation, or achievement'),

    handleValidationErrors
];

// ================================
// PROFILE UPDATE VALIDATION
// ================================
const validateProfileUpdate = [
    body('firstName')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/)
        .withMessage('First name can only contain letters and spaces')
        .trim(),

    body('lastName')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/)
        .withMessage('Last name can only contain letters and spaces')
        .trim(),

    body('mobile')
        .optional()
        .isMobilePhone('en-IN')
        .withMessage('Please provide a valid Indian mobile number'),

    body('email')
        .optional()
        .isEmail()
        .withMessage('Please provide a valid email address')
        .normalizeEmail(),

    body('dateOfBirth')
        .optional()
        .isISO8601()
        .withMessage('Please provide a valid date of birth')
        .custom((value) => {
            const dob = new Date(value);
            const age = new Date().getFullYear() - dob.getFullYear();
            if (age < 16 || age > 65) {
                throw new Error('Age must be between 16 and 65 years');
            }
            return true;
        }),

    body('collegeName')
        .optional()
        .isLength({ min: 2, max: 100 })
        .withMessage('College name must be between 2 and 100 characters')
        .trim(),

    body('department')
        .optional()
        .isIn(['CSE', 'ECE', 'EEE', 'ME', 'CE', 'IT', 'OTHER'])
        .withMessage('Invalid department'),

    body('semester')
        .optional()
        .isInt({ min: 1, max: 8 })
        .withMessage('Semester must be between 1 and 8'),

    body('passingYear')
        .optional()
        .isInt({ min: 2020, max: 2030 })
        .withMessage('Passing year must be between 2020 and 2030'),

    body('preferredLocation')
        .optional()
        .isIn(['Vijaynagar', 'Gulbarga', 'Belagavi', 'Other'])
        .withMessage('Invalid preferred location'),

    handleValidationErrors
];

// ================================
// QUERY PARAMETER VALIDATIONS
// ================================
const validatePaginationQuery = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),

    query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),

    handleValidationErrors
];

const validateDateRangeQuery = [
    query('startDate')
        .optional()
        .isISO8601()
        .withMessage('Start date must be a valid ISO 8601 date'),

    query('endDate')
        .optional()
        .isISO8601()
        .withMessage('End date must be a valid ISO 8601 date')
        .custom((endDate, { req }) => {
            if (req.query.startDate && endDate) {
                const start = new Date(req.query.startDate);
                const end = new Date(endDate);
                if (end <= start) {
                    throw new Error('End date must be after start date');
                }
            }
            return true;
        }),

    handleValidationErrors
];

const validateBatchIdParam = [
    param('batchId')
        .isMongoId()
        .withMessage('Invalid batch ID'),

    handleValidationErrors
];

const validateMaterialIdParam = [
    param('materialId')
        .isMongoId()
        .withMessage('Invalid material ID'),

    handleValidationErrors
];

const validateAssignmentIdParam = [
    param('assignmentId')
        .isMongoId()
        .withMessage('Invalid assignment ID'),

    handleValidationErrors
];

const validateAssessmentIdParam = [
    param('assessmentId')
        .isMongoId()
        .withMessage('Invalid assessment ID'),

    handleValidationErrors
];

const validateNotificationIdParam = [
    param('notificationId')
        .isMongoId()
        .withMessage('Invalid notification ID'),

    handleValidationErrors
];

const validateCertificateIdParam = [
    param('certificateId')
        .isMongoId()
        .withMessage('Invalid certificate ID'),

    handleValidationErrors
];

const validateFeedbackIdParam = [
    param('feedbackId')
        .isMongoId()
        .withMessage('Invalid feedback ID'),

    handleValidationErrors
];

// ================================
// ATTENDANCE QUERY VALIDATION
// ================================
const validateAttendanceQuery = [
    query('batchId')
        .optional()
        .isMongoId()
        .withMessage('Invalid batch ID'),

    query('month')
        .optional()
        .isInt({ min: 1, max: 12 })
        .withMessage('Month must be between 1 and 12'),

    query('year')
        .optional()
        .isInt({ min: 2020, max: 2030 })
        .withMessage('Year must be between 2020 and 2030'),

    // If month is provided, year should also be provided
    query('month')
        .custom((month, { req }) => {
            if (month && !req.query.year) {
                throw new Error('Year is required when month is provided');
            }
            return true;
        }),

    handleValidationErrors
];

// ================================
// RESULTS QUERY VALIDATION
// ================================
const validateResultsQuery = [
    query('batchId')
        .optional()
        .isMongoId()
        .withMessage('Invalid batch ID'),

    query('type')
        .optional()
        .isIn(['quiz', 'assignment', 'midterm', 'final', 'project'])
        .withMessage('Invalid assessment type'),

    handleValidationErrors
];

// ================================
// PROGRESS QUERY VALIDATION
// ================================
const validateProgressQuery = [
    query('batchId')
        .optional()
        .isMongoId()
        .withMessage('Invalid batch ID'),

    query('period')
        .optional()
        .isIn(['1month', '3months', '6months', '1year'])
        .withMessage('Invalid period. Must be 1month, 3months, 6months, or 1year'),

    handleValidationErrors
];

// ================================
// NOTIFICATION QUERY VALIDATION
// ================================
const validateNotificationQuery = [
    query('type')
        .optional()
        .isIn(['general', 'assignment', 'material', 'assessment', 'grade', 'feedback', 'certificate_request'])
        .withMessage('Invalid notification type'),

    query('read')
        .optional()
        .isBoolean()
        .withMessage('Read status must be a boolean value'),

    handleValidationErrors
];

// ================================
// FILE UPLOAD VALIDATION
// ================================
const validateFileUpload = (req, res, next) => {
    if (!req.file) {
        throw new ApiError(400, 'Please upload a file');
    }

    // File size validation (10MB max)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (req.file.size > maxSize) {
        throw new ApiError(400, 'File size must not exceed 10MB');
    }

    // File type validation for assignments
    const allowedMimeTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'image/jpeg',
        'image/png',
        'application/zip',
        'application/x-zip-compressed'
    ];

    if (!allowedMimeTypes.includes(req.file.mimetype)) {
        throw new ApiError(400, 'Invalid file type. Allowed types: PDF, DOC, DOCX, TXT, JPEG, PNG, ZIP');
    }

    next();
};

// ================================
// CUSTOM VALIDATION FUNCTIONS
// ================================
const validateStudentEnrollment = async (req, res, next) => {
    try {
        const { batchId } = req.params;
        const studentId = req.user._id;

        if (!batchId) {
            return next();
        }

        const Batch = require('../models/Batch');
        const batch = await Batch.findOne({ _id: batchId, students: studentId });

        if (!batch) {
            throw new ApiError(403, 'You are not enrolled in this batch');
        }

        next();
    } catch (error) {
        next(error);
    }
};

const validateAssignmentDeadline = async (req, res, next) => {
    try {
        const { assignmentId } = req.params;

        if (!assignmentId) {
            return next();
        }

        const Assignment = require('../models/Assignment');
        const assignment = await Assignment.findById(assignmentId);

        if (!assignment) {
            throw new ApiError(404, 'Assignment not found');
        }

        // Allow submission if not past deadline or if updating existing submission
        const now = new Date();
        const isUpdate = req.method === 'PUT';

        if (!isUpdate && now > assignment.dueDate) {
            throw new ApiError(400, 'Assignment submission deadline has passed');
        }

        next();
    } catch (error) {
        next(error);
    }
};

// ================================
// COMBINED VALIDATION CHAINS
// ================================
const validateSubmissionWithFile = [
    validateAssignmentIdParam,
    validateFileUpload,
    validateSubmitAssignment
];

const validateFeedbackSubmission = [
    validateFeedback,
    // Additional custom validation for feedback uniqueness can be added here
];

const validateCertificateRequestWithEligibility = [
    validateCertificateRequest,
    // Additional custom validation for certificate eligibility can be added here
];

// Export all validations
module.exports = {
    // Basic validations
    validateSubmitAssignment,
    validateFeedback,
    validateCertificateRequest,
    validateProfileUpdate,

    // Parameter validations
    validateBatchIdParam,
    validateMaterialIdParam,
    validateAssignmentIdParam,
    validateAssessmentIdParam,
    validateNotificationIdParam,
    validateCertificateIdParam,
    validateFeedbackIdParam,

    // Query validations
    validatePaginationQuery,
    validateDateRangeQuery,
    validateAttendanceQuery,
    validateResultsQuery,
    validateProgressQuery,
    validateNotificationQuery,

    // File upload validations
    validateFileUpload,

    // Custom validations
    validateStudentEnrollment,
    validateAssignmentDeadline,

    // Combined validations
    validateSubmissionWithFile,
    validateFeedbackSubmission,
    validateCertificateRequestWithEligibility,

    // Utility
    handleValidationErrors
};