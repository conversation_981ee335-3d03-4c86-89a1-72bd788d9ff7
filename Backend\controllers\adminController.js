const asyncHandler = require('express-async-handler');

const ApiError = require('../utils/apiError');
const ApiResponse = require('../utils/apiResponse');
const User = require('../models/User');
const Batch = require('../models/Batch');
const Course = require('../models/Course');
const College = require('../models/College');
const Certificate = require('../models/Certificate');
const bcrypt = require('bcryptjs');
const { generateUserId } = require('../utils/helpers');
const logger = require('../utils/logger');
const csvParser = require('csv-parser');
const fs = require('fs');
const { sendOtpEmail } = require('../services/emailSender');

// ==================== USER MANAGEMENT ====================

const createStudent = asyncHandler(async (req, res) => {
    const {
        firstName,
        lastName,
        email,
        mobile,
        whatsappNo,
        dateOfBirth,
        studentType, // 'student' or 'professional'
        collegeName,
        companyName,
        passingYear,
        department,
        semester,
        preferredLocation,
        password
    } = req.body;

    if (!firstName || !lastName || !email || !mobile || !dateOfBirth || !studentType) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    const existingUser = await User.findOne({
        $or: [
            { email: email.toLowerCase() },
            { mobile }
        ]
    });

    if (existingUser) {
        throw new ApiError(409, 'User already exists with this email or mobile number');
    }

    // Generate student ID
    const studentId = await generateUserId('STU');

    // Hash password
    const hashedPassword = await bcrypt.hash(password || 'student123', 12);

    //  Generate OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit
    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Create student with OTP info
    const student = await User.create({
        userId: studentId,
        firstName,
        lastName,
        email: email.toLowerCase(),
        mobile,
        whatsapp: whatsappNo || mobile,
        dob: new Date(dateOfBirth),
        role: 'student',
        isProfessional: studentType === 'professional',
        collegeName: studentType === 'student' ? collegeName : null,
        companyName: studentType === 'professional' ? companyName : null,
        passingYear,
        department,
        semester,
        preferredLocation,
        password: hashedPassword,
        status: 'active',
        otp: {
            code: otp,
            expiresAt: otpExpiry,
            verified: false,
            attempts: 0,
            lastSent: new Date(),
            resendResetTime: new Date(Date.now() + 15 * 60 * 1000) // optional limit window
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: req.user._id
    });

    //  Send OTP Email
    await sendOtpEmail(email, otp);

    // Return success without password
    const { password: _, otp: __, ...studentData } = student.toObject();

    logger.info(`Student created with OTP: ${studentId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, studentData, 'Student created successfully. OTP sent to email.')
    );
});

const createTrainer = asyncHandler(async (req, res) => {
    const {
        firstName,
        lastName,
        email,
        mobile,
        dateOfBirth,
        qualification,
        experience,
        specialization,
        preferredLocation,
        password
    } = req.body;

    if (!firstName || !lastName || !email || !mobile || !qualification || !experience) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    const existingUser = await User.findOne({
        $or: [
            { email: email.toLowerCase() },
            { mobile }
        ]
    });

    if (existingUser) {
        throw new ApiError(409, 'User already exists with this email or mobile number');
    }

    const trainerId = await generateUserId('TRN');

    const hashedPassword = await bcrypt.hash(password || 'trainer123', 12);

    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiry = Date.now() + 10 * 60 * 1000; // 10 minutes

    const trainer = await User.create({
        userId: trainerId,
        name: {
            first: firstName,
            last: lastName
        },
        email: email.toLowerCase(),
        mobile,
        dob: new Date(dateOfBirth),
        role: 'trainer',
        qualification,
        experience,
        otherDetails: specialization,
        preferredLocation,
        password: hashedPassword,
        otp: {
            code: otpCode,
            expiresAt: new Date(otpExpiry),
            verified: false,
            attempts: 0
        },
        isActive: true,
        createdBy: req.user._id
    });

    await sendOtpEmail(email, otpCode);

    const { password: _, otp: otpField, ...trainerData } = trainer.toObject();

    logger.info(`Trainer created successfully: ${trainerId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, trainerData, 'Trainer created successfully. OTP sent to email.')
    );
});

const createCollege = asyncHandler(async (req, res) => {
    const {
        firstName,
        lastName,
        email,
        mobile,
        collegeName,
        collegeCode,
        address,
        city,
        state,
        departments,
        password
    } = req.body;

    //  Validation
    if (!firstName || !lastName || !email || !mobile || !collegeName || !collegeCode) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    //  Check if college already exists by collegeCode
    const existingCollege = await College.findOne({
        collegeCode: collegeCode.toUpperCase()
    });


    if (existingCollege) {
        throw new ApiError(409, 'College already exists with this code');
    }

    //  Check if user already exists
    const existingUser = await User.findOne({
        $or: [
            { email: email.toLowerCase() },
            { mobile }
        ]
    });

    if (existingUser) {
        throw new ApiError(409, 'User already exists with this email or mobile number');
    }

    //  Generate College ID
    const collegeId = await generateUserId('COL');

    const hashedPassword = await bcrypt.hash(password || 'college123', 12);

    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiry = Date.now() + 10 * 60 * 1000;

    const college = await College.create({
        collegeId,
        collegeName,
        collegeCode: collegeCode.toUpperCase(),
        address,
        city,
        state,
        departments: departments || [],
        isActive: true,
        createdBy: req.user._id
    });

    const collegeUser = await User.create({
        userId: collegeId,
        name: {
            first: firstName,
            last: lastName
        },
        email: email.toLowerCase(),
        mobile,
        role: 'college',
        collegeName,
        collegeCode: collegeCode.toUpperCase(),
        password: hashedPassword,
        otp: {
            code: otpCode,
            expiresAt: new Date(otpExpiry),
            verified: false,
            attempts: 0
        },
        isActive: true,
        createdBy: req.user._id
    });

    await sendOtpEmail(email, otpCode);

    const { password: _, otp, ...collegeUserData } = collegeUser.toObject();

    logger.info(`College created successfully: ${collegeId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, {
            college,
            user: collegeUserData
        }, 'College created successfully. OTP sent to registered email.')
    );
});


const getAllUsers = asyncHandler(async (req, res) => {
    const { role, page = 1, limit = 10, search } = req.query;

    const filter = {};
    if (role) filter.role = role;
    if (search) {
        filter.$or = [
            { firstName: { $regex: search, $options: 'i' } },
            { lastName: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
            { userId: { $regex: search, $options: 'i' } }
        ];
    }

    const users = await User.find(filter)
        .select('-password')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const totalUsers = await User.countDocuments(filter);

    res.status(200).json(
        new ApiResponse(200, {
            users,
            totalPages: Math.ceil(totalUsers / limit),
            currentPage: page,
            totalUsers
        }, 'Users retrieved successfully')
    );
});
const getUserReport = asyncHandler(async (req, res) => {
    const { userId } = req.params;
    const user = await User.findById(userId)
        .select('-password')
        .populate('assignedBatches', 'batchId batchName startDate endDate status');

    if (!user) {
        throw new ApiError(404, 'User not found');
    }

    let report = {
        user: {
            userId: user._id, // use _id if userId doesn't exist
            fullName: `${user?.name?.first || ''} ${user?.name?.last || ''}`.trim(),
            email: user.email,
            mobile: user.mobile,
            role: user.role,
            isActive: user.isActive,
            createdAt: user.createdAt
        }
    };

    if (user.role === 'student') {
        report.studentDetails = {
            studentType: user.studentType,
            collegeName: user.collegeName,
            companyName: user.companyName,
            department: user.department,
            semester: user.semester,
            passingYear: user.passingYear,
            preferredLocation: user.preferredLocation
        };
    } else if (user.role === 'trainer') {
        report.trainerDetails = {
            qualification: user.qualification,
            experience: user.experience,
            specialization: user.specialization,
            assignedBatches: user.assignedBatches
        };
    } else if (user.role === 'college') {
        report.collegeDetails = {
            collegeName: user.collegeName,
            collegeCode: user.collegeCode
        };
    }

    res.status(200).json(
        new ApiResponse(200, report, 'User report retrieved successfully')
    );
});

const toggleUserStatus = asyncHandler(async (req, res) => {
    const { userId } = req.params;

    const user = await User.findOne({ userId });
    if (!user) {
        throw new ApiError(404, 'User not found');
    }

    user.isActive = !user.isActive;
    await user.save();

    res.status(200).json(
        new ApiResponse(200, {
            userId: user.userId,
            isActive: user.isActive
        }, `User ${user.isActive ? 'activated' : 'deactivated'} successfully`)
    );
});

const updateUser = asyncHandler(async (req, res) => {
    const { userId } = req.params;
    const updates = req.body;

    // Remove fields that shouldn't be updated
    delete updates.password;
    delete updates.userId;
    delete updates.role;
    delete updates.createdAt;
    delete updates.updatedAt;

    const user = await User.findOneAndUpdate(
        { userId },
        { ...updates, updatedAt: new Date() },
        { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
        throw new ApiError(404, 'User not found');
    }

    res.status(200).json(
        new ApiResponse(200, user, 'User updated successfully')
    );
});


const deleteUser = asyncHandler(async (req, res) => {
    const { userId } = req.params;

    const user = await User.findByIdAndUpdate(
        userId, // Using _id instead of custom field
        {
            isActive: false,
            deletedAt: new Date(),
            deletedBy: req.user._id
        },
        { new: true }
    ).select('-password');

    if (!user) {
        throw new ApiError(404, 'User not found');
    }

    res.status(200).json(
        new ApiResponse(200, user, 'User deleted successfully')
    );
});



const createBatch = asyncHandler(async (req, res) => {
    const {
        batchName,
        startDate,
        endDate,
        maxStudents,
        location,
        schedule,
        description
    } = req.body;

    // Validation
    if (!batchName || !startDate || !endDate || !maxStudents) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    // Check if course exists
    // const course = await Course.findById(courseId);
    // if (!course) {
    //     throw new ApiError(404, 'Course not found');
    // }

    // Generate batch ID
    const batchId = await generateUserId('BAT');

    // Create batch
    const batch = await Batch.create({
        batchId,
        batchName,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        maxStudents,
        location,
        schedule,
        description,
        status: 'upcoming',
        createdBy: req.user._id
    });

    // Populate course details
    await batch.populate('courseId', 'title duration');

    logger.info(`Batch created successfully: ${batchId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, batch, 'Batch created successfully')
    );
});

const assignBatchToTrainer = asyncHandler(async (req, res) => {
    const { batchId, trainerId } = req.body;

    // Validation
    if (!batchId || !trainerId) {
        throw new ApiError(400, 'Please provide batch ID and trainer ID');
    }

    // Check if batch exists
    const batch = await Batch.findOne({ batchId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    // Check if trainer exists
    const trainer = await User.findOne({ _id: trainerId, role: 'trainer' }); // Use _id as per your User model
    if (!trainer) {
        throw new ApiError(404, 'Trainer not found');
    }

    // Check if trainer is already assigned to this batch
    if (batch.trainerId && batch.trainerId.equals(trainer._id)) {
        throw new ApiError(409, 'Trainer is already assigned to this batch');
    }

    // Assign trainer to batch
    batch.trainerId = trainer._id;
    await batch.save();

    // Update trainer's assigned batches
    trainer.assignedBatches = trainer.assignedBatches || [];
    if (!trainer.assignedBatches.includes(batch._id)) {
        trainer.assignedBatches.push(batch._id);
        await trainer.save();
    }

    // Populate trainer details
    await batch.populate('trainerId', 'userId name.first name.last email mobile');
    logger.info(`Trainer ${trainerId} assigned to batch ${batchId} by admin: ${req.user.userId}`);

    res.status(200).json(
        new ApiResponse(200, batch, 'Trainer assigned to batch successfully')
    );
});


const getAllBatches = asyncHandler(async (req, res) => {
    const { page = 1, limit = 10, status } = req.query;

    const filter = {};
    if (status) filter.status = status;

    const batches = await Batch.find(filter)
        .populate('trainerId', 'name.first name.last email userId')
        .populate('students', 'name.first name.last email userId')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);
    const totalBatches = await Batch.countDocuments(filter);

    res.status(200).json(
        new ApiResponse(200, {
            batches,
            totalPages: Math.ceil(totalBatches / limit),
            currentPage: page,
            totalBatches
        }, 'Batches retrieved successfully')
    );
});


const getBatchReport = asyncHandler(async (req, res) => {
    const { batchId } = req.params;

    // Find batch with populated data
    const batch = await Batch.findOne({ batchId })
        .populate('course', 'courseName courseCode duration')
        .populate('trainers', 'userId firstName lastName email mobile')
        .populate('students', 'userId firstName lastName email mobile department collegeName');

    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    // Get additional statistics
    const totalStudents = batch.students.length;
    const maxStudents = batch.maxStudents;
    const occupancyRate = totalStudents > 0 ? ((totalStudents / maxStudents) * 100).toFixed(2) : 0;

    const report = {
        batch: {
            batchId: batch.batchId,
            batchName: batch.batchName,
            startDate: batch.startDate,
            endDate: batch.endDate,
            status: batch.status,
            location: batch.location,
            schedule: batch.schedule
        },
        course: batch.course,
        trainers: batch.trainers,
        students: batch.students,
        statistics: {
            totalStudents,
            maxStudents,
            occupancyRate: `${occupancyRate}%`,
            availableSlots: maxStudents - totalStudents
        }
    };

    res.status(200).json(
        new ApiResponse(200, report, 'Batch report retrieved successfully')
    );
});


const updateBatchStatus = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const { status } = req.body;

    const validStatuses = ['active', 'inactive', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
        throw new ApiError(400, 'Invalid status. Valid statuses: ' + validStatuses.join(', '));
    }

    const batch = await Batch.findOneAndUpdate(
        { batchId },
        { status, updatedAt: new Date() },
        { new: true }
    ).populate('course', 'courseName courseCode');

    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    res.status(200).json(
        new ApiResponse(200, batch, 'Batch status updated successfully')
    );
});


const removeStudentFromBatch = asyncHandler(async (req, res) => {
    const { batchId, studentId } = req.params;

    const batch = await Batch.findOne({ batchId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    const student = await User.findOne({ userId: studentId });
    if (!student) {
        throw new ApiError(404, 'Student not found');
    }

    // Remove student from batch
    batch.students = batch.students.filter(
        id => !id.equals(student._id)
    );
    await batch.save();

    // Remove batch from student's assigned batches
    student.assignedBatches = student.assignedBatches.filter(
        id => !id.equals(batch._id)
    );
    await student.save();

    res.status(200).json(
        new ApiResponse(200, {
            batchId: batch.batchId,
            studentId: student.userId,
            remainingStudents: batch.students.length
        }, 'Student removed from batch successfully')
    );
});

// ==================== COURSE MANAGEMENT ====================


const uploadCourse = asyncHandler(async (req, res) => {
    const {
        courseName,
        courseCode,
        description,
        duration,
        difficulty,
        prerequisites,
        objectives,
        modules
    } = req.body;

    if (!courseName || !courseCode || !duration) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    const existingCourse = await Course.findOne({
        courseCode: courseCode.toUpperCase()
    });

    if (existingCourse) {
        throw new ApiError(409, 'Course already exists with this code');
    }

    let courseFile = null;
    if (req.file && req.file.path) {
        const uploaded = await uploadToCloudinary(req.file.path, 'course-materials');
        courseFile = uploaded.url;
    }

    const course = await Course.create({
        courseName,
        courseCode: courseCode.toUpperCase(),
        description,
        duration,
        difficulty: difficulty || 'intermediate',
        prerequisites: prerequisites || [],
        objectives: objectives || [],
        modules: modules || [],
        courseFile,
        isActive: true,
        createdBy: req.user._id
    });

    logger.info(`Course created: ${courseCode} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, course, 'Course created successfully')
    );
});


const getAllCourses = asyncHandler(async (req, res) => {
    const { page = 1, limit = 10, search } = req.query;

    const filter = {};
    if (search) {
        filter.$or = [
            { courseName: { $regex: search, $options: 'i' } },
            { courseCode: { $regex: search, $options: 'i' } }
        ];
    }

    const courses = await Course.find(filter)
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const totalCourses = await Course.countDocuments(filter);

    res.status(200).json(
        new ApiResponse(200, {
            courses,
            totalPages: Math.ceil(totalCourses / limit),
            currentPage: page,
            totalCourses
        }, 'Courses retrieved successfully')
    );
});

// ==================== CERTIFICATE MANAGEMENT ====================



const uploadCertificate = asyncHandler(async (req, res) => {
    const {
        studentId,
        batchId,
        courseId,
        certificateType,
        issueDate,
        grade,
        remarks
    } = req.body;

    // Validate required fields
    if (!studentId || !batchId || !courseId || !certificateType) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    // Validate existence
    const student = await User.findOne({ userId: studentId, role: 'student' });
    if (!student) throw new ApiError(404, 'Student not found');

    const batch = await Batch.findOne({ batchId });
    if (!batch) throw new ApiError(404, 'Batch not found');

    const course = await Course.findById(courseId);
    if (!course) throw new ApiError(404, 'Course not found');

    const existingCertificate = await Certificate.findOne({
        student: student._id,
        batch: batch._id,
        course: courseId
    });

    if (existingCertificate) {
        throw new ApiError(409, 'Certificate already exists for this student and batch');
    }

    // Upload file to Cloudinary if present
    let certificateFile = null;
    if (req.file && req.file.path) {
        const uploaded = await uploadToCloudinary(req.file.path, 'certificates');
        certificateFile = uploaded.url;
    }

    const certificateId = await generateUserId('CRT');

    const certificate = await Certificate.create({
        certificateId,
        student: student._id,
        batch: batch._id,
        course: courseId,
        certificateType,
        issueDate: new Date(issueDate || Date.now()),
        grade,
        remarks,
        certificateFile,
        status: 'issued',
        issuedBy: req.user._id
    });

    await certificate.populate([
        { path: 'student', select: 'userId firstName lastName email' },
        { path: 'batch', select: 'batchId batchName' },
        { path: 'course', select: 'courseName courseCode' }
    ]);

    logger.info(`Certificate issued: ${certificateId} for student: ${studentId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, certificate, 'Certificate uploaded successfully')
    );
});

// ==================== DASHBOARD ROUTES ====================


const getDashboardStats = asyncHandler(async (req, res) => {
    // Get counts for different entities
    const [
        totalUsers,
        totalStudents,
        totalTrainers,
        totalColleges,
        totalBatches,
        totalCourses,
        totalCertificates,
        activeBatches,
        activeStudents,
        activeTrainers
    ] = await Promise.all([
        User.countDocuments(),
        User.countDocuments({ role: 'student' }),
        User.countDocuments({ role: 'trainer' }),
        User.countDocuments({ role: 'college' }),
        Batch.countDocuments(),
        Course.countDocuments(),
        Certificate.countDocuments(),
        Batch.countDocuments({ status: 'active' }),
        User.countDocuments({ role: 'student', isActive: true }),
        User.countDocuments({ role: 'trainer', isActive: true })
    ]);

    // Get recent registrations (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentRegistrations = await User.countDocuments({
        createdAt: { $gte: thirtyDaysAgo }
    });

    // Get completion rate
    const completedBatches = await Batch.countDocuments({ status: 'completed' });
    const completionRate = totalBatches > 0 ? ((completedBatches / totalBatches) * 100).toFixed(2) : 0;

    const stats = {
        overview: {
            totalUsers,
            totalStudents,
            totalTrainers,
            totalColleges,
            totalBatches,
            totalCourses,
            totalCertificates
        },
        active: {
            activeBatches,
            activeStudents,
            activeTrainers
        },
        metrics: {
            recentRegistrations,
            completionRate: `${completionRate}%`,
            averageStudentsPerBatch: totalBatches > 0 ? Math.round(totalStudents / totalBatches) : 0
        }
    };

    res.status(200).json(
        new ApiResponse(200, stats, 'Dashboard statistics retrieved successfully')
    );
});


const getRecentActivities = asyncHandler(async (req, res) => {
    const { limit = 10 } = req.query;

    // Get recent user registrations
    const recentUsers = await User.find({})
        .select('userId firstName lastName role createdAt')
        .sort({ createdAt: -1 })
        .limit(limit / 2);

    // Get recent batch creations
    const recentBatches = await Batch.find({})
        .select('batchId batchName createdAt')
        .populate('course', 'courseName')
        .sort({ createdAt: -1 })
        .limit(limit / 2);

    // Get recent certificates
    const recentCertificates = await Certificate.find({})
        .select('certificateId certificateType issueDate')
        .populate('student', 'userId firstName lastName')
        .sort({ issueDate: -1 })
        .limit(limit / 2);

    // Combine and sort all activities
    const activities = [
        ...recentUsers.map(user => ({
            type: 'user_registration',
            description: `New ${user.role} registered: ${user.firstName} ${user.lastName}`,
            userId: user.userId,
            timestamp: user.createdAt
        })),
        ...recentBatches.map(batch => ({
            type: 'batch_creation',
            description: `New batch created: ${batch.batchName} for ${batch.course.courseName}`,
            batchId: batch.batchId,
            timestamp: batch.createdAt
        })),
        ...recentCertificates.map(cert => ({
            type: 'certificate_issued',
            description: `Certificate issued to ${cert.student.firstName} ${cert.student.lastName}`,
            certificateId: cert.certificateId,
            timestamp: cert.issueDate
        }))
    ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, limit);

    res.status(200).json(
        new ApiResponse(200, activities, 'Recent activities retrieved successfully')
    );
});
const bulkCreateStudents = asyncHandler(async (req, res) => {
    if (!req.file) {
        throw new ApiError(400, 'CSV file is required');
    }

    const results = [];
    fs.createReadStream(req.file.path)
        .pipe(csvParser())
        .on('data', (data) => results.push(data))
        .on('end', async () => {
            const created = [];
            for (const row of results) {
                try {
                    const {
                        firstName,
                        lastName,
                        email,
                        mobile,
                        dateOfBirth,
                        studentType,
                        collegeName,
                        companyName,
                        passingYear,
                        department,
                        semester,
                        preferredLocation
                    } = row;

                    const existing = await User.findOne({ $or: [{ email }, { mobile }] });
                    if (existing) continue;

                    const userId = await generateUserId('STU');
                    const hashedPassword = await bcrypt.hash('student123', 12);

                    await User.create({
                        userId,
                        firstName,
                        lastName,
                        email: email.toLowerCase(),
                        mobile,
                        whatsappNo: mobile,
                        dateOfBirth: new Date(dateOfBirth),
                        role: 'student',
                        studentType,
                        collegeName: studentType === 'student' ? collegeName : null,
                        companyName: studentType === 'professional' ? companyName : null,
                        passingYear,
                        department,
                        semester,
                        preferredLocation,
                        password: hashedPassword,
                        isActive: true,
                        createdBy: req.user._id
                    });
                    created.push(email);
                } catch (error) {
                    logger.error('Error in row: ', row, error);
                }
            }
            res.status(201).json(new ApiResponse(201, { created }, 'Bulk students created'));
        });
});

const bulkAssignStudents = asyncHandler(async (req, res) => {
    const { batchId, studentIds } = req.body;
    if (!batchId || !Array.isArray(studentIds)) {
        throw new ApiError(400, 'Batch ID and student IDs are required');
    }

    const batch = await Batch.findOne({ batchId });
    if (!batch) throw new ApiError(404, 'Batch not found');

    let count = 0;
    for (const studentId of studentIds) {
        const student = await User.findOne({ userId: studentId, role: 'student' });
        if (!student) continue;

        if (!batch.students.includes(student._id)) {
            batch.students.push(student._id);
            student.assignedBatches = student.assignedBatches || [];
            student.assignedBatches.push(batch._id);
            await student.save();
            count++;
        }
    }
    await batch.save();

    res.status(200).json(new ApiResponse(200, { assignedCount: count }, 'Students assigned to batch'));
});

const getDepartmentWiseReport = asyncHandler(async (req, res) => {
    const report = await User.aggregate([
        { $match: { role: 'student', department: { $exists: true } } },
        { $group: { _id: '$department', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
    ]);

    res.status(200).json(new ApiResponse(200, report, 'Department wise report'));
});

const getBatchPerformanceReport = asyncHandler(async (req, res) => {
    const certificates = await Certificate.find()
        .populate('batch', 'batchId batchName')
        .populate('student', 'userId firstName lastName')
        .populate('course', 'courseName');

    const report = certificates.map(cert => ({
        certificateId: cert.certificateId,
        batch: cert.batch?.batchName,
        student: `${cert.student?.firstName} ${cert.student?.lastName}`,
        course: cert.course?.courseName,
        grade: cert.grade,
        remarks: cert.remarks,
        issueDate: cert.issueDate
    }));

    res.status(200).json(new ApiResponse(200, report, 'Batch performance report'));
});
/**
 * Monthly Registration Report
 *  GET /api/admin/report/monthly-registrations
 *  Private (Admin only)
 */
const getMonthlyRegistrationReport = asyncHandler(async (req, res) => {
    const report = await User.aggregate([
        {
            $match: {
                createdAt: { $gte: new Date(new Date().getFullYear(), 0, 1) }
            }
        },
        {
            $group: {
                _id: { month: { $month: '$createdAt' } },
                total: { $sum: 1 }
            }
        },
        { $sort: { '_id.month': 1 } }
    ]);

    res.status(200).json(new ApiResponse(200, report, 'Monthly registrations report'));
});

module.exports = {
    createStudent,
    createTrainer,
    createCollege,
    createBatch,
    assignBatchToTrainer,
    uploadCourse,
    getAllCourses,
    getAllUsers,
    uploadCertificate,
    getDashboardStats,
    getRecentActivities,
    bulkCreateStudents,
    bulkAssignStudents,
    getDepartmentWiseReport,
    getBatchPerformanceReport,
    getMonthlyRegistrationReport,
    getUserReport,
    toggleUserStatus,
    updateUser,
    deleteUser,
    getAllBatches,
    getBatchReport,
    updateBatchStatus,
    removeStudentFromBatch
};