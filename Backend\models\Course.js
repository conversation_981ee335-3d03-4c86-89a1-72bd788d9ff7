const mongoose = require('mongoose');

const courseSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true
    },

    description: {
        type: String
    },

    syllabus: {
        type: String
    },

    duration: {
        type: String // e.g., "6 weeks", "3 months"
    },

    department: {
        type: String // Optional: e.g., "CSE", "ECE"
    },

    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', // Admin or Trainer
        required: true
    },

    materials: [
        {
            title: String,
            type: {
                type: String,
                enum: ['pdf', 'ppt', 'video', 'link']
            },
            fileUrl: String,
            uploadedAt: {
                type: Date,
                default: Date.now
            }
        }
    ],

    assignments: [
        {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Assignment'
        }
    ],

    createdAt: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true
});

module.exports = mongoose.model('Course', courseSchema);
