const express = require('express');
const router = express.Router();
const studentController = require('../controllers/studentController');
const { protect, authorize } = require('../middlewares/authMiddleware');
const { validateSubmitAssignment, validateFeedback, validateCertificateRequest } = require('../middlewares/studenvelidationMiddleware');
const upload = require('../middlewares/uploadMiddleware');

// Apply authentication and student role check to all routes
router.use(protect);              // middleware to check JWT
router.use(authorize('student')); // only students can access these routes

// ================================
// STUDENT DASHBOARD
// ================================
router.get('/dashboard', studentController.getDashboard);

// ================================
// BATCH & COURSE INFORMATION
// ================================
router.get('/batches', studentController.getEnrolledBatches);
router.get('/batches/:batchId', studentController.getBatchDetails);
router.get('/batches/:batchId/course', studentController.getCourseDetails);

// ================================
// COURSE MATERIALS
// ================================
router.get('/batches/:batchId/materials', studentController.getBatchMaterials);
router.get('/materials/:materialId', studentController.getMaterialDetails);
router.get('/materials/:materialId/download', studentController.downloadMaterial);

// ================================
// ASSIGNMENTS
// ================================
router.get('/batches/:batchId/assignments', studentController.getBatchAssignments);
router.get('/assignments/:assignmentId', studentController.getAssignmentDetails);
router.post('/assignments/:assignmentId/submit',
    upload.single('file'),
    validateSubmitAssignment,
    studentController.submitAssignment
);
router.get('/assignments/:assignmentId/submission', studentController.getMySubmission);
router.put('/assignments/:assignmentId/submission',
    upload.single('file'),
    studentController.updateSubmission
);

// ================================
// ATTENDANCE
// ================================
router.get('/attendance', studentController.getMyAttendance);
router.get('/batches/:batchId/attendance', studentController.getBatchAttendance);
router.get('/attendance/summary', studentController.getAttendanceSummary);

// ================================
// MARKS & RESULTS
// ================================
router.get('/results', studentController.getMyResults);
router.get('/batches/:batchId/results', studentController.getBatchResults);
router.get('/assessments/:assessmentId/result', studentController.getAssessmentResult);
router.get('/progress', studentController.getMyProgress);

// ================================
// FEEDBACK
// ================================
router.get('/feedback', studentController.getMyFeedback);
router.post('/feedback',
    validateFeedback,
    studentController.submitFeedback
);
router.get('/feedback/:feedbackId', studentController.getFeedbackDetails);
router.put('/feedback/:feedbackId', studentController.updateFeedback);

// ================================
// CERTIFICATES
// ================================
router.get('/certificates', studentController.getMyCertificates);
router.post('/certificates/request',
    validateCertificateRequest,
    studentController.requestCertificate
);
router.get('/certificates/:certificateId', studentController.getCertificateDetails);
router.get('/certificates/:certificateId/download', studentController.downloadCertificate);

// ================================
// NOTIFICATIONS
// ================================
router.get('/notifications', studentController.getNotifications);
router.put('/notifications/:notificationId/read', studentController.markNotificationAsRead);
router.put('/notifications/mark-all-read', studentController.markAllNotificationsAsRead);

// ================================
// PROFILE MANAGEMENT
// ================================
router.get('/profile', studentController.getProfile);
router.put('/profile', studentController.updateProfile);

// ================================
// REPORTS & ANALYTICS
// ================================
router.get('/reports/performance', studentController.getPerformanceReport);
router.get('/reports/attendance', studentController.getAttendanceReport);

module.exports = router;